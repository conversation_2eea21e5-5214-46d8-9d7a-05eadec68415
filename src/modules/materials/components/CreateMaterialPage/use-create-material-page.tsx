import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import { brandOptions } from "src/modules/brand/hooks/brand-options";
import {
	categoryOptions,
	categorySubcategoriesOptions,
} from "src/modules/category/hooks/category-options";
import { CategoryCode } from "src/modules/category/service/model/category";
import { measurementUnitOptions } from "src/modules/measurement-unit/hooks/measurement-unit-options";
import { useCreateProduct } from "src/modules/product";
import { CreateMaterialSchema } from "./schema";

const defaultValues = {} as CreateMaterialSchema;

export default function useCreateMaterialPage() {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useCreateProduct();

	// Fetch required data for dropdowns
	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);
	const { data: categories = [] } = useQuery(categoryOptions(service));

	// Find the materials parent category
	const materialParentCategory = categories.find(
		(cat) => cat.code === CategoryCode.MATERIALS,
	);

	// Fetch subcategories for materials
	const { data: materialSubcategories = [] } = useQuery(
		categorySubcategoriesOptions(service, materialParentCategory?.id || ""),
	);

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateMaterialSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					name: value.name,
					commercialName: value.commercialName,
					code: value.code,
					skuCode: value.skuCode,
					brandID: value.brandID,
					measurementUnitID: value.measurementUnitID,
					categoryIDs: materialParentCategory?.id
						? [...value.categoryIDs, materialParentCategory?.id]
						: value.categoryIDs,
					state: value.state,
					description: value.description || undefined,
					canBeSold: value.canBeSold,
					canBePurchased: value.canBePurchased,
					costPrice: value.costPrice,
				},
				{
					onSuccess: () => {
						toast.success("Material creado exitosamente");
						navigate({ to: "/admin/products/materials" });
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	return {
		form,
		isPending,
		brands,
		measurementUnits,
		materialSubcategories,
		materialParentCategory,
	};
}
